{"name": "obsidian-ghost-sync", "version": "1.0.0", "description": "Sync posts between Obsidian and Ghost.io", "main": "src/main.ts", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "test": "vitest run", "test:ci": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:legacy": "node test-content-conversion.js", "test:build": "npm test && npm run build", "test:e2e": "npx vitest run --config vitest.playwright.config.mjs", "test:e2e:headless": "xvfb-run -a --server-args=\"-screen 0 1280x1024x24 -ac -nolisten tcp -dpi 96\" npx vitest run --config vitest.playwright.config.mjs", "test:e2e:windowed": "E2E_HEADLESS=false npx vitest run --config vitest.playwright.config.mjs", "test:e2e:ci": "CI=true npx vitest run --config vitest.playwright.config.mjs --reporter=verbose", "setup:headless": "bash scripts/setup-headless-testing.sh", "test:headless-detection": "node scripts/test-headless-detection.js", "test:xvfb": "node scripts/test-xvfb-electron.js", "validate:headless": "node scripts/validate-headless-setup.js", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@electron/asar": "^4.0.1", "@playwright/test": "^1.48.0", "@sveltejs/vite-plugin-svelte": "^6.1.3", "@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "@vitest/ui": "^3.2.4", "builtin-modules": "^5.0.0", "chai": "^5.2.1", "electron": "^37.3.1", "esbuild": "^0.25.9", "esbuild-svelte": "^0.9.3", "jsdom": "^26.0.0", "obsidian": "latest", "svelte": "^5.38.1", "ts-node": "^10.9.2", "tslib": "^2.8.1", "typescript": "^5.9.2", "vite": "^7.1.3", "vitest": "^3.2.4"}, "dependencies": {"@lexical/code": "^0.34.0", "@lexical/headless": "^0.34.0", "@lexical/html": "^0.34.0", "@lexical/link": "^0.34.0", "@lexical/list": "^0.34.0", "@lexical/markdown": "^0.34.0", "@lexical/rich-text": "^0.34.0", "@lexical/table": "^0.34.0", "@lexical/text": "^0.34.0", "@lexical/utils": "^0.34.0", "@tryghost/admin-api": "^1.14.0", "@types/markdown-it": "^14.1.2", "lexical": "^0.34.0", "markdown-it": "^14.1.0", "turndown": "^7.2.0"}}