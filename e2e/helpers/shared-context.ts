import type { Page, ElectronApplication } from 'playwright';
import { resetObsidianUI } from './plugin-setup';
import * as fs from 'fs';
import * as path from 'path';
import { randomUUID } from 'crypto';
import { beforeAll, beforeEach, afterEach, afterAll } from 'vitest';

/**
 * Shared context for all e2e tests
 * Provides access to the global Electron instance with worker isolation
 */
export interface SharedTestContext {
  page: Page;
  electronApp: ElectronApplication;
  testId: string;
  vaultPath: string;
  dataPath: string;
  workerId: string;
}

// Map of worker-specific contexts for parallel execution
const workerContexts = new Map<string, SharedTestContext>();

/**
 * Get the current worker ID for test isolation
 * Uses process.env.VITEST_WORKER_ID or falls back to process.pid
 */
function getWorkerId(): string {
  // Vitest sets VITEST_WORKER_ID for each worker process
  const workerId = process.env.VITEST_WORKER_ID ||
    process.env.VITEST_POOL_ID ||
    process.pid.toString();
  return `worker-${workerId}`;
}

/**
 * Get the shared test context with worker isolation
 */
export async function getSharedTestContext(): Promise<SharedTestContext> {
  const workerId = getWorkerId();

  // Check if we already have a context for this worker
  const existingContext = workerContexts.get(workerId);
  if (existingContext) {
    return existingContext;
  }

  console.log(`🔧 Creating test environment for ${workerId}`);

  // Create isolated environment for this worker
  const { testId, vaultPath, dataPath } = await createIsolatedTestEnvironment(workerId);

  // Create Electron instance using simplified setup
  const { setupObsidianElectron } = await import('./plugin-setup');
  const { electronApp, page } = await setupObsidianElectron(vaultPath, dataPath);

  const context: SharedTestContext = {
    electronApp,
    page,
    testId,
    vaultPath,
    dataPath,
    workerId
  };

  // Store context for this worker
  workerContexts.set(workerId, context);
  console.log(`✅ Test environment ready for ${workerId}`);

  return context;
}

/**
 * Reset UI state for the next test
 * This should be called in beforeEach hooks
 */
export async function resetSharedTestContext(): Promise<void> {
  const context = await getSharedTestContext();
  await resetObsidianUI(context.page);
}

/**
 * Clean up test-specific state
 * This should be called in afterEach hooks
 */
export async function cleanupTestState(): Promise<void> {
  const context = await getSharedTestContext();
  await resetObsidianUI(context.page);

  // Clear any test files from the worker's isolated articles directory
  const articlesDir = path.join(context.vaultPath, 'articles');
  if (fs.existsSync(articlesDir)) {
    const files = fs.readdirSync(articlesDir);
    for (const file of files) {
      if (file.includes('test-') && file.endsWith('.md')) {
        try {
          fs.unlinkSync(path.join(articlesDir, file));
        } catch (error) {
          console.log(`⚠️ Could not remove test file ${file}:`, error.message);
        }
      }
    }
  }
}

/**
 * Clean up all worker contexts and isolated environments
 * This should be called during global teardown
 */
export async function cleanupAllWorkerContexts(): Promise<void> {
  console.log('🧹 Cleaning up all worker contexts...');

  for (const [workerId, context] of workerContexts.entries()) {
    try {
      console.log(`🧹 Cleaning up ${workerId}...`);

      // Close Electron app for this worker
      if (context.electronApp) {
        try {
          // Get the process PID before closing for force cleanup if needed
          const electronProcess = context.electronApp.process();
          const pid = electronProcess?.pid;

          if (context.page && !context.page.isClosed()) {
            await context.page.close();
          }

          // Try graceful close first
          await context.electronApp.close();
          console.log(`✅ Gracefully closed Electron app for ${workerId}`);

          // If we have a PID and the process is still running, force kill it
          if (pid) {
            try {
              process.kill(pid, 0); // Check if process still exists
              console.log(`🔪 Force killing remaining process ${pid} for ${workerId}`);
              process.kill(pid, 'SIGTERM');

              // Give it a moment to terminate
              await new Promise(resolve => setTimeout(resolve, 500));

              // Check if it's still running and force kill if needed
              try {
                process.kill(pid, 0);
                process.kill(pid, 'SIGKILL');
                console.log(`💀 Force killed process ${pid} for ${workerId}`);
              } catch {
                // Process already terminated
              }
            } catch {
              // Process already terminated or doesn't exist
            }
          }
        } catch (error) {
          console.log(`⚠️ Error closing Electron app for ${workerId}:`, error.message);
        }
      }

      // Clean up isolated environment
      await cleanupIsolatedTestEnvironment(context.testId);

    } catch (error) {
      console.log(`⚠️ Error cleaning up ${workerId}:`, error.message);
    }
  }

  workerContexts.clear();
  console.log('✅ All worker contexts cleaned up');
}

/**
 * Capture a screenshot for debugging purposes
 */
export async function captureScreenshotOnFailure(context: SharedTestContext, name: string): Promise<void> {
  try {
    const screenshotsDir = path.join(process.cwd(), 'e2e', 'screenshots');
    await fs.promises.mkdir(screenshotsDir, { recursive: true });

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${name}-${timestamp}-${context.workerId}.png`;
    const screenshotPath = path.join(screenshotsDir, filename);

    await context.page.screenshot({
      path: screenshotPath,
      fullPage: true,
      type: 'png'
    });

    console.log(`📸 Screenshot captured: ${screenshotPath}`);
  } catch (error) {
    console.warn(`⚠️ Failed to capture screenshot: ${error.message}`);
  }
}

export async function executeCommand(context: SharedTestContext, command: string): Promise<void> {
  // Take a screenshot before attempting command execution for debugging
  await captureScreenshotOnFailure(context, `before-command-${command.replace(/[^a-zA-Z0-9]/g, '-')}`);

  // Press the command palette shortcut
  await context.page.keyboard.press('Meta+P');

  // Wait for command palette to appear with multiple possible selectors
  const commandPaletteSelectors = [
    '.prompt-input',           // Standard Obsidian command palette input
    '.suggestion-input',       // Alternative selector
    '.modal-container input',  // Input within modal container
    '.suggester-container input', // Input within suggester container
    'input[placeholder*="command"]', // Input with command-related placeholder
    'input[placeholder*="Type"]',    // Input with "Type" placeholder
    '.modal input',            // Any input in a modal
    '.prompt input'            // Input in prompt container
  ];

  let commandInput: any = null;
  let usedSelector = '';

  // Try each selector with a shorter timeout
  for (const selector of commandPaletteSelectors) {
    try {
      await context.page.waitForSelector(selector, { timeout: 2000 });
      commandInput = await context.page.$(selector);
      if (commandInput) {
        usedSelector = selector;
        console.log(`✅ Found command palette input using selector: ${selector}`);
        break;
      }
    } catch (error) {
      console.log(`⚠️ Selector ${selector} not found, trying next...`);
      continue;
    }
  }

  if (!commandInput) {
    // Take a screenshot for debugging
    await captureScreenshotOnFailure(context, `command-palette-not-found-${command.replace(/[^a-zA-Z0-9]/g, '-')}`);

    // Log available elements for debugging
    const availableElements = await context.page.evaluate(() => {
      const inputs = Array.from(document.querySelectorAll('input'));
      const modals = Array.from(document.querySelectorAll('.modal-container, .suggester-container, .prompt'));
      return {
        inputs: inputs.map(input => ({
          tagName: input.tagName,
          className: input.className,
          placeholder: input.placeholder,
          type: input.type,
          visible: input.offsetParent !== null
        })),
        modals: modals.map(modal => ({
          className: modal.className,
          visible: modal.offsetParent !== null,
          innerHTML: modal.innerHTML.substring(0, 200)
        }))
      };
    });

    console.error('❌ Command palette input not found. Available elements:', JSON.stringify(availableElements, null, 2));
    throw new Error(`Command palette input not found. Tried selectors: ${commandPaletteSelectors.join(', ')}`);
  }

  // Fill the command and execute
  await context.page.fill(usedSelector, command);
  await context.page.keyboard.press('Enter');

  // Wait a moment for the command to execute
  await context.page.waitForTimeout(500);
}

/**
 * Wait for and check if a notice with specific text appears
 */
export async function waitForNotice(context: SharedTestContext, expectedText: string, timeout: number = 5000): Promise<boolean> {
  try {
    await context.page.waitForFunction(
      ({ text }) => {
        const noticeElements = document.querySelectorAll('.notice');
        const notices = Array.from(noticeElements).map(el => el.textContent || '');
        return notices.some(notice => notice.includes(text));
      },
      { text: expectedText },
      { timeout }
    );
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Wait for and verify that a notice with specific text appears using expect
 */
export async function expectNotice(context: SharedTestContext, expectedText: string, timeout: number = 5000): Promise<void> {
  const { expect } = await import('vitest');
  const noticeAppeared = await waitForNotice(context, expectedText, timeout);
  expect(noticeAppeared).toBe(true);
}

/**
 * Get post file information for testing
 */
export async function getPostFile(
  context: SharedTestContext,
  filename: string
): Promise<{ exists: boolean; title?: string; content?: string; path?: string }> {
  return await context.page.evaluate(
    async ({ filename }) => {
      const app = (window as any).app;

      const articlesPath = 'articles';
      const fullPath = `${articlesPath}/${filename}.md`;

      const file = app.vault.getAbstractFileByPath(fullPath);
      if (!file) {
        return { exists: false, path: fullPath };
      }

      const fileCache = app.metadataCache.getFileCache(file);
      const frontmatter = fileCache?.frontmatter || {};
      const fullContent = await app.vault.read(file);

      let bodyContent = fullContent;
      if (fileCache?.frontmatterPosition) {
        const frontmatterEnd = fileCache.frontmatterPosition.end;
        bodyContent = fullContent.slice(frontmatterEnd.offset).trim();
      }

      return {
        exists: true,
        title: frontmatter.title,
        content: bodyContent,
        path: fullPath
      };
    },
    { filename }
  );
}

/**
 * Check if a post file exists and optionally validate its properties using expect
 */
export async function expectPostFile(
  context: SharedTestContext,
  filename: string,
  options?: { title?: string; content?: RegExp }
): Promise<void> {
  const { expect } = await import('vitest');
  const fileData = await getPostFile(context, filename);

  expect(fileData.exists).toBe(true);

  if (options?.title) {
    expect(fileData.title).toBe(options.title);
  }

  if (options?.content) {
    expect(fileData.content).toMatch(options.content);
  }
}

/**
 * Create an isolated test environment with unique vault and data directories
 * @param workerId Optional worker ID for consistent naming across worker processes
 */
export async function createIsolatedTestEnvironment(workerId?: string): Promise<{ testId: string; vaultPath: string; dataPath: string }> {
  const testId = workerId || randomUUID();
  const baseTestDir = path.resolve('./e2e/test-environments');
  const testDir = path.join(baseTestDir, testId);
  const vaultPath = path.join(testDir, 'vault');
  const dataPath = path.join(testDir, 'data');

  // Ensure base test directory exists
  await fs.promises.mkdir(baseTestDir, { recursive: true });
  await fs.promises.mkdir(testDir, { recursive: true });

  // Copy pristine vault to isolated vault
  const pristineVaultPath = path.resolve('./tests/vault/Test.pristine');
  if (fs.existsSync(pristineVaultPath)) {
    await copyDirectory(pristineVaultPath, vaultPath);
  } else {
    throw new Error('Pristine vault not found at tests/vault/Test.pristine');
  }

  // Copy pristine data directory to isolated data directory
  const pristineDataPath = path.resolve('./e2e/obsidian-data.pristine');
  if (fs.existsSync(pristineDataPath)) {
    await copyDirectory(pristineDataPath, dataPath);

    // Update obsidian.json to point to the isolated vault path
    const obsidianJsonPath = path.join(dataPath, 'obsidian.json');
    if (fs.existsSync(obsidianJsonPath)) {
      try {
        const obsidianConfig = JSON.parse(await fs.promises.readFile(obsidianJsonPath, 'utf8'));

        // Update all vault paths to point to the isolated vault
        if (obsidianConfig.vaults) {
          for (const vaultId in obsidianConfig.vaults) {
            obsidianConfig.vaults[vaultId].path = vaultPath;
          }
        }

        await fs.promises.writeFile(obsidianJsonPath, JSON.stringify(obsidianConfig, null, 2));
        console.log(`📝 Updated obsidian.json vault path to: ${vaultPath}`);
      } catch (error) {
        console.warn(`⚠️ Failed to update obsidian.json vault path: ${error.message}`);
      }
    }
  } else {
    // Create minimal data directory if pristine doesn't exist
    await fs.promises.mkdir(dataPath, { recursive: true });
  }

  // Update plugin configuration with .env values
  await updatePluginConfigWithEnvValues(vaultPath);

  return { testId, vaultPath, dataPath };
}

/**
 * Clean up isolated test environment
 */
export async function cleanupIsolatedTestEnvironment(testId: string): Promise<void> {
  const baseTestDir = path.resolve('./e2e/test-environments');
  const testDir = path.join(baseTestDir, testId);

  if (fs.existsSync(testDir)) {
    try {
      await fs.promises.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      console.log(`⚠️ Failed to cleanup test environment ${testId}:`, error.message);
    }
  }
}

/**
 * Update plugin configuration with .env values
 */
async function updatePluginConfigWithEnvValues(vaultPath: string): Promise<void> {
  const pluginDataPath = path.join(vaultPath, '.obsidian/plugins/ghost-sync/data.json');

  if (!fs.existsSync(pluginDataPath)) {
    console.log('⚠️ Plugin data.json not found, skipping .env integration');
    return;
  }

  try {
    // Load environment settings
    const envSettings = loadEnvironmentSettings();

    // Read existing plugin configuration
    const existingConfig = JSON.parse(await fs.promises.readFile(pluginDataPath, 'utf8'));

    // Update with .env values if they exist
    const updatedConfig = {
      ...existingConfig,
      ...(envSettings.ghostUrl && { ghostUrl: envSettings.ghostUrl }),
      ...(envSettings.ghostAdminApiKey && { ghostAdminApiKey: envSettings.ghostAdminApiKey })
    };

    // Write updated configuration
    await fs.promises.writeFile(pluginDataPath, JSON.stringify(updatedConfig, null, 2));
    console.log(`📝 Updated plugin configuration with .env values`);

  } catch (error) {
    console.warn(`⚠️ Failed to update plugin configuration: ${error.message}`);
  }
}

/**
 * Load environment settings from .env file and process.env
 */
function loadEnvironmentSettings(): { ghostUrl?: string; ghostAdminApiKey?: string } {
  const envSettings: { ghostUrl?: string; ghostAdminApiKey?: string } = {};

  // Try to read from .env file
  const envPath = path.resolve('.env');
  if (fs.existsSync(envPath)) {
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            let value = valueParts.join('=').trim();
            // Remove quotes if present
            if ((value.startsWith('"') && value.endsWith('"')) ||
              (value.startsWith("'") && value.endsWith("'"))) {
              value = value.slice(1, -1);
            }

            if (key.trim() === 'GHOST_URL') {
              envSettings.ghostUrl = value;
            } else if (key.trim() === 'GHOST_ADMIN_API_KEY') {
              envSettings.ghostAdminApiKey = value;
            }
          }
        }
      }
    } catch (error) {
      console.log('⚠️ Could not read .env file:', error.message);
    }
  }

  // Override with process.env if available
  if (process.env.GHOST_URL) {
    envSettings.ghostUrl = process.env.GHOST_URL;
  }
  if (process.env.GHOST_ADMIN_API_KEY) {
    envSettings.ghostAdminApiKey = process.env.GHOST_ADMIN_API_KEY;
  }

  return envSettings;
}

/**
 * Recursively copy directory
 */
async function copyDirectory(src: string, dest: string): Promise<void> {
  await fs.promises.mkdir(dest, { recursive: true });

  const entries = await fs.promises.readdir(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.promises.copyFile(srcPath, destPath);
    }
  }
}

/**
 * Sets up common e2e test hooks and returns a context proxy
 * This eliminates duplication of beforeAll/beforeEach/afterEach/afterAll setup across test files
 *
 * Usage:
 * ```typescript
 * describe("My Test", () => {
 *   const context = setupE2ETestHooks();
 *
 *   test("my test", async () => {
 *     await context.page.click(...);
 *   });
 * });
 * ```
 */
export function setupE2ETestHooks(): SharedTestContext {
  let context: SharedTestContext;

  beforeAll(async () => {
    context = await getSharedTestContext();
  });

  beforeEach(async () => {
    await resetSharedTestContext();
  });

  afterEach(async (testContext) => {
    // Capture screenshot on test failure
    if (testContext?.meta?.result?.state === 'fail') {
      const testName = testContext.meta.name?.replace(/[^a-zA-Z0-9]/g, '-') || 'unknown-test';
      await captureScreenshotOnFailure(context, `test-failure-${testName}`);
    }

    await cleanupTestState();
  });

  afterAll(async () => {
    // Force cleanup of any remaining processes
    try {
      if (context?.electronApp) {
        await context.electronApp.close();
      }
    } catch (error) {
      console.log('⚠️ Error during afterAll cleanup:', error.message);
    }
  });

  // Return a Proxy that delegates to the context once it's available
  return new Proxy({} as SharedTestContext, {
    get(target, prop) {
      if (!context) {
        throw new Error(`Context not yet initialized. Make sure you're accessing context properties inside test functions, not at the top level.`);
      }
      return context[prop as keyof SharedTestContext];
    },
    set(target, prop, value) {
      if (!context) {
        throw new Error(`Context not yet initialized. Make sure you're accessing context properties inside test functions, not at the top level.`);
      }
      (context as any)[prop] = value;
      return true;
    }
  });
}
